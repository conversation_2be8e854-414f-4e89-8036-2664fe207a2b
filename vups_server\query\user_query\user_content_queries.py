"""
Content queries module.
Handles videos, dynamics, comments, and related content queries.
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import asyncpg
from vups.logger import logger
import vups.utils as U
from vups_server.base.query_base import BaseQueryService
from vups_server.sql.sentence import user_sql


class UserContentQueryService(BaseQueryService):
    """Service for querying user content (videos, dynamics, comments)."""

    def __init__(self):
        super().__init__(cache_ttl=600)  # 10 minutes cache for content

    async def get_user_info_by_uid(self, uid: str) -> Optional[asyncpg.Record]:
        """
        Query user's basic information by UID.

        Args:
            uid: User UID

        Returns:
            User information record or None if not found
        """
        cache_key = f"user_info_{uid}"

        query = """
            SELECT uid, name, face, sign, birthday, top_photo, room_id, live_url
            FROM user_info_table
            WHERE uid = $1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Successfully retrieved user info for UID={uid}")
        else:
            logger.info(f"No user info found for UID={uid}")

        return result

    async def get_user_dynamics(self, uid: str) -> List[Dict]:
        """
        Query all dynamics for a given user with heat calculation.

        Args:
            uid: User UID

        Returns:
            List of dynamics with calculated heat values
        """
        cache_key = f"user_dynamics_{uid}"

        query = """
            SELECT name, timestamp, datetime, dynamic_content, url, topic,
                   dynamic_id, share_num, comment_num, like_num, comment_id,
                   comment_type, heat
            FROM dynamics_table
            WHERE uid = $1
            ORDER BY datetime DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetch"
        )

        dynamics_with_heat = []
        if results:
            for row in results:
                try:
                    share_num = self._safe_int(row["share_num"])
                    comment_num = self._safe_int(row["comment_num"])
                    like_num = self._safe_int(row["like_num"])

                    heat = round(
                        U.dynamic_calculate_hotness(share_num, comment_num, like_num),
                        2,
                    )

                    content = row["dynamic_content"]
                    if content:
                        cleaned_content = re.sub(r"\[.*?\]", "", content).strip()
                        if cleaned_content:
                            dynamics_with_heat.append({
                                "id": row["comment_id"],
                                "name": row["name"],
                                "content": cleaned_content.replace("\n", " "),
                                "topic": row["topic"],
                                "url": row["url"],
                                "heat": heat,
                                "pubtime": (
                                    row["datetime"].strftime("%Y-%m-%d")
                                    if row["datetime"]
                                    else None
                                ),
                                "share_num": share_num,
                                "comment_num": comment_num,
                                "like_num": like_num,
                            })
                except Exception as e:
                    logger.error(f"Error processing dynamic {row.get('dynamic_id', 'N/A')}: {e}")
                    continue

        return dynamics_with_heat

    async def get_user_videos(self, uid: str) -> List[Dict]:
        """
        Query all videos for a given user with heat calculation.

        Args:
            uid: User UID

        Returns:
            List of videos with calculated heat values
        """
        cache_key = f"user_videos_{uid}"

        query = """
            SELECT name, bvid, timestamp, datetime, video_name, description, cover,
                   play_num, comment_num, like_num, coin, favorite_num, share_num,
                   danmuku_num, aid, length, honor_short, honor_count, honor,
                   video_ai_conclusion, heat
            FROM videos_table
            WHERE uid = $1
            ORDER BY datetime DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetch"
        )

        videos_with_heat = []
        if results:
            for row in results:
                try:
                    play_num = self._safe_int(row["play_num"])
                    comment_num = self._safe_int(row["comment_num"])
                    honor_count = self._safe_int(row["honor_count"])
                    like_num = self._safe_int(row["like_num"])
                    coin_num = self._safe_int(row["coin"])
                    favorite_num = self._safe_int(row["favorite_num"])

                    heat = round(
                        U.video_calculate_hotness(
                            play_num,
                            comment_num,
                            honor_count,
                            like_num,
                            coin_num,
                            favorite_num,
                        ),
                        2,
                    )

                    videos_with_heat.append({
                        "bvid": row["bvid"],
                        "name": row["video_name"],
                        "description": row["description"],
                        "face": row["cover"],
                        "heat": heat,
                        "play_num": play_num,
                        "comment_num": comment_num,
                        "like_num": like_num,
                        "coin_num": coin_num,
                        "favorite_num": favorite_num,
                        "danmaku_num": row["danmuku_num"],
                        "share_num": row["share_num"],
                        "aid": row["aid"],
                        "length": row["length"],
                        "pubtime": (
                            row["datetime"].strftime("%Y-%m-%d")
                            if row["datetime"]
                            else None
                        ),
                        "honor_short": (
                            str(row["honor_short"]) if row["honor_short"] else ""
                        ),
                        "honor": row["honor"],
                        "honor_count": honor_count,
                        "video_ai_conclusion": row["video_ai_conclusion"],
                    })
                except Exception as e:
                    logger.error(f"Error calculating heat for video {row.get('bvid', 'N/A')}: {e}")
                    continue

        return videos_with_heat

    async def get_latest_video(self, uid: str) -> Optional[List]:
        """
        Query the most recent video for a given user.

        Args:
            uid: User UID

        Returns:
            List containing latest video information or None values if not found
        """
        cache_key = f"latest_video_{uid}"

        query = """
            SELECT bvid, video_name, description, cover, datetime, play_num,
                   comment_num, like_num, coin, favorite_num, share_num,
                   danmuku_num, aid, length, honor_short, honor_count, honor,
                   video_ai_conclusion
            FROM videos_table
            WHERE uid = $1
            ORDER BY datetime DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        if result:
            logger.info(f"Retrieved latest video for UID={uid}: {result['bvid']}")
            return [
                result["bvid"],
                result["video_name"],
                result["description"],
                result["cover"],
                (
                    result["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                    if result["datetime"]
                    else None
                ),
                result["play_num"],
                result["comment_num"],
                result["like_num"],
                result["coin"],
                result["favorite_num"],
                result["share_num"],
                result["danmuku_num"],
                result["aid"],
                result["length"],
                result["honor_short"],
                result["honor_count"],
                result["honor"],
                result["video_ai_conclusion"],
            ]
        else:
            logger.warning(f"No videos found for UID={uid}")
            return [None] * 18

    async def get_latest_dynamic(self, uid: str) -> Optional[List]:
        """
        Query the most recent dynamic for a given user.

        Args:
            uid: User UID

        Returns:
            List containing latest dynamic information or None values if not found
        """
        cache_key = f"latest_dynamic_{uid}"

        query = """
            SELECT name, datetime, dynamic_content, url, topic, dynamic_id,
                   share_num, comment_num, like_num, comment_id, comment_type
            FROM dynamics_table
            WHERE uid = $1
            ORDER BY datetime DESC
            LIMIT 1
        """

        result = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid],
            fetch_type="fetchrow"
        )

        if result:
            dynamic_content = result["dynamic_content"]
            if dynamic_content:
                dynamic_content = re.sub(r'[\xa0\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u2028\u2029\u3000]', ' ', dynamic_content)
                dynamic_content = dynamic_content.encode('utf-8', errors='replace').decode('utf-8')

            logger.info(f"Retrieved latest dynamic for UID={uid}: {dynamic_content}")
            return [
                result["name"],
                (
                    result["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                    if result["datetime"]
                    else None
                ),
                result["dynamic_content"].encode('utf-8', errors='ignore').decode('utf-8') if result["dynamic_content"] else None,
                result["url"],
                result["topic"],
                result["dynamic_id"],
                result["share_num"],
                result["comment_num"],
                result["like_num"],
                result["comment_id"],
                result["comment_type"],
            ]
        else:
            logger.warning(f"No dynamics found for UID={uid}")
            return [None] * 11

    async def get_recent_top_videos(self, uid: str, limit: int = 10) -> List[Dict]:
        """
        Query recent top videos for a user based on heat.

        Args:
            uid: User UID
            limit: Maximum number of videos to return

        Returns:
            List of top videos sorted by heat
        """
        cache_key = f"recent_top_videos_{uid}_{limit}"

        query = """
            SELECT bvid, video_name, description, cover, datetime, play_num,
                   comment_num, like_num, coin, favorite_num, share_num,
                   danmuku_num, aid, length, honor_short, honor_count, honor,
                   video_ai_conclusion, heat
            FROM videos_table
            WHERE uid = $1 AND datetime >= NOW() - INTERVAL '90 days'
            ORDER BY heat DESC, datetime DESC
            LIMIT $2
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[uid, limit],
            fetch_type="fetch"
        )

        videos = []
        if results:
            for row in results:
                videos.append({
                    "bvid": row["bvid"],
                    "name": row["video_name"],
                    "description": row["description"],
                    "face": row["cover"],
                    "heat": row["heat"],
                    "play_num": row["play_num"],
                    "comment_num": row["comment_num"],
                    "like_num": row["like_num"],
                    "coin_num": row["coin"],
                    "favorite_num": row["favorite_num"],
                    "danmaku_num": row["danmuku_num"],
                    "share_num": row["share_num"],
                    "aid": row["aid"],
                    "length": row["length"],
                    "pubtime": (
                        row["datetime"].strftime("%Y-%m-%d")
                        if row["datetime"]
                        else None
                    ),
                    "honor_short": (
                        str(row["honor_short"]) if row["honor_short"] else ""
                    ),
                    "honor": row["honor"],
                    "honor_count": row["honor_count"],
                    "video_ai_conclusion": row["video_ai_conclusion"],
                })

        return videos

    async def get_video_comments(self, uid: str) -> List[Dict]:
        """
        Query all video comments for a user.

        Args:
            uid: User UID

        Returns:
            List of video comments
        """
        cache_key = f"video_comments_{uid}"

        comment_table = f"video_comment_{uid}"

        # Check if comment table exists
        table_exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = $1
            )
        """

        table_exists = await self._execute_query(
            table_exists_query, [comment_table], "fetchval"
        )

        if not table_exists:
            logger.warning(f"Video comment table {comment_table} does not exist")
            return []

        query = f"""
            SELECT bvid, comment_id, comment_content, like_num, reply_num,
                   comment_time, comment_user_name, comment_user_uid,
                   comment_user_face, comment_user_level, comment_user_vip_type,
                   comment_user_vip_status, comment_user_vip_due_date,
                   comment_user_vip_label_text, comment_user_vip_avatar_subscript,
                   comment_user_vip_nickname_color, comment_user_vip_role,
                   comment_user_vip_title, comment_user_vip_title_type,
                   comment_user_pendant_pid, comment_user_pendant_name,
                   comment_user_pendant_image, comment_user_pendant_expire,
                   comment_user_nameplate_nid, comment_user_nameplate_name,
                   comment_user_nameplate_image, comment_user_nameplate_image_small,
                   comment_user_nameplate_level, comment_user_nameplate_condition,
                   comment_user_official_role, comment_user_official_title,
                   comment_user_official_desc, comment_user_official_type,
                   comment_user_level_info_current_level,
                   comment_user_level_info_current_min,
                   comment_user_level_info_current_exp,
                   comment_user_level_info_next_exp, comment_user_is_senior_member,
                   comment_user_is_uploader, comment_user_is_contractor,
                   comment_user_contract_desc, comment_user_nft_avatar,
                   comment_user_nft_region, comment_user_nft_region_type,
                   comment_user_nft_icon, comment_user_nft_show_status,
                   comment_user_avatar_item_id, comment_user_avatar_item_type,
                   comment_user_avatar_subscript_url, comment_user_avatar_jump_url,
                   comment_user_avatar_identifytype, comment_user_avatar_text,
                   comment_user_avatar_text_color, comment_user_avatar_bg_color,
                   comment_user_avatar_border_color, comment_user_avatar_bg_style,
                   comment_user_avatar_bg_color_night,
                   comment_user_avatar_border_color_night,
                   comment_user_garb_url_portrait, comment_user_garb_url_landscape,
                   comment_user_garb_url_carousel, comment_user_garb_url_thumbnail,
                   comment_user_garb_loading_url, comment_user_garb_type,
                   comment_user_garb_fan_color, comment_user_garb_is_fan,
                   comment_user_garb_fan_mid, comment_user_garb_fan_number,
                   comment_user_garb_fan_num_desc, comment_user_garb_fan_num_color,
                   comment_user_garb_fan_num_color_night, comment_user_garb_fan_color_night
            FROM {comment_table}
            ORDER BY comment_time DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[],
            fetch_type="fetch"
        )

        comments = []
        if results:
            for row in results:
                comments.append({
                    "bvid": row["bvid"],
                    "comment_id": row["comment_id"],
                    "content": row["comment_content"],
                    "like_num": row["like_num"],
                    "reply_num": row["reply_num"],
                    "time": row["comment_time"],
                    "user_name": row["comment_user_name"],
                    "user_uid": row["comment_user_uid"],
                    "user_face": row["comment_user_face"],
                    "user_level": row["comment_user_level"],
                })

        return comments

    async def get_dynamics_comments(self, uid: str) -> List[Dict]:
        """
        Query all dynamics comments for a user.

        Args:
            uid: User UID

        Returns:
            List of dynamics comments
        """
        cache_key = f"dynamics_comments_{uid}"

        comment_table = f"dynamics_comment_{uid}"

        # Check if comment table exists
        table_exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = $1
            )
        """

        table_exists = await self._execute_query(
            table_exists_query, [comment_table], "fetchval"
        )

        if not table_exists:
            logger.warning(f"Dynamics comment table {comment_table} does not exist")
            return []

        query = f"""
            SELECT dynamic_id, comment_id, comment_content, like_num, reply_num,
                   comment_time, comment_user_name, comment_user_uid,
                   comment_user_face, comment_user_level
            FROM {comment_table}
            ORDER BY comment_time DESC
        """

        results = await self._cached_query(
            cache_key=cache_key,
            query=query,
            params=[],
            fetch_type="fetch"
        )

        comments = []
        if results:
            for row in results:
                comments.append({
                    "dynamic_id": row["dynamic_id"],
                    "comment_id": row["comment_id"],
                    "content": row["comment_content"],
                    "like_num": row["like_num"],
                    "reply_num": row["reply_num"],
                    "time": row["comment_time"],
                    "user_name": row["comment_user_name"],
                    "user_uid": row["comment_user_uid"],
                    "user_face": row["comment_user_face"],
                    "user_level": row["comment_user_level"],
                })

        return comments

    async def get_comments_for_wordcloud(self, uid: str, limit: int = 1000) -> List[str]:
        """
        Query comments for word cloud generation.

        Args:
            uid: User UID
            limit: Maximum number of comments to retrieve

        Returns:
            List of comment content strings
        """
        cache_key = f"wordcloud_comments_{uid}_{limit}"

        # Get video comments
        video_table = f"video_comment_{uid}"
        video_exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = $1
            )
        """

        video_table_exists = await self._execute_query(
            video_exists_query, [video_table], "fetchval"
        )

        comments = []

        if video_table_exists:
            video_query = f"""
                SELECT comment_content
                FROM {video_table}
                WHERE comment_content IS NOT NULL
                AND LENGTH(comment_content) > 5
                ORDER BY comment_time DESC
                LIMIT $1
            """

            video_results = await self._execute_query(
                video_query, [limit // 2], "fetch"
            )

            if video_results:
                comments.extend([row["comment_content"] for row in video_results])

        # Get dynamics comments
        dynamics_table = f"dynamics_comment_{uid}"
        dynamics_exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = $1
            )
        """

        dynamics_table_exists = await self._execute_query(
            dynamics_exists_query, [dynamics_table], "fetchval"
        )

        if dynamics_table_exists:
            dynamics_query = f"""
                SELECT comment_content
                FROM {dynamics_table}
                WHERE comment_content IS NOT NULL
                AND LENGTH(comment_content) > 5
                ORDER BY comment_time DESC
                LIMIT $1
            """

            dynamics_results = await self._execute_query(
                dynamics_query, [limit // 2], "fetch"
            )

            if dynamics_results:
                comments.extend([row["comment_content"] for row in dynamics_results])

        # Cache the results
        if comments:
            await self.cache.set(cache_key, comments)

        return comments[:limit]


# Global instance for easy access
user_content_service = UserContentQueryService()
